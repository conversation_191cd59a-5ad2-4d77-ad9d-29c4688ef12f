<?php
/**
 * Vista para la gestión de balances de gastos fijos
 *
 * Variables disponibles:
 * @var array $gastos_fijos Lista de gastos fijos activos
 * @var string $error_display Estado de visualización de errores ('show' o 'hide')
 * @var string $error_text Texto del mensaje de error
 * @var string $success_display Estado de visualización de éxito ('show' o 'hide')
 * @var string $success_text Texto del mensaje de éxito
 */

// Get current month and year for defaults
$current_date = new DateTime('now', new DateTimeZone('America/Bogota'));
$current_month = (int)$current_date->format('n');
$current_year = (int)$current_date->format('Y');
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Gestión de Balances de Gastos Fijos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <!-- Toast Notifications CSS -->
    <link href="resources/css/toast-notifications.css" rel="stylesheet" />
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">

                    <?php #region PAGE HEADER ?>
                    <div class="d-flex align-items-center mb-3">
                        <div>
                            <h4 class="mb-0">Gestión de Balances de Gastos Fijos</h4>
                            <p class="mb-0 text-muted">Administración de balances mensuales de gastos fijos</p>
                        </div>

                    </div>

                    <hr>
                    <?php #endregion PAGE HEADER ?>

                    <?php #region SEARCH FILTERS ?>
                    <!-- Search Filters Panel -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Filtros de Búsqueda</h4>
                        </div>
                        <div class="panel-body">
                            <form id="search-form">
                                <div class="row g-3">
                                    <!-- Gasto Fijo Filter -->
                                    <div class="col-md-4">
                                        <label for="id_gasto_fijo" class="form-label">Gasto Fijo:</label>
                                        <select class="form-select" id="id_gasto_fijo" name="id_gasto_fijo">
                                            <option value="">Seleccione un gasto fijo</option>
                                            <?php foreach ($gastos_fijos as $gasto_fijo): ?>
                                                <option value="<?= $gasto_fijo->getId() ?>"><?= htmlspecialchars($gasto_fijo->getDescripcion()) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <!-- Month Filter -->
                                    <div class="col-md-2">
                                        <label for="mes" class="form-label">Mes: <span class="text-danger">*</span></label>
                                        <select class="form-select" id="mes" name="mes" required>
                                            <option value="">Mes</option>
                                            <option value="1" <?= $current_month === 1 ? 'selected' : '' ?>>Enero</option>
                                            <option value="2" <?= $current_month === 2 ? 'selected' : '' ?>>Febrero</option>
                                            <option value="3" <?= $current_month === 3 ? 'selected' : '' ?>>Marzo</option>
                                            <option value="4" <?= $current_month === 4 ? 'selected' : '' ?>>Abril</option>
                                            <option value="5" <?= $current_month === 5 ? 'selected' : '' ?>>Mayo</option>
                                            <option value="6" <?= $current_month === 6 ? 'selected' : '' ?>>Junio</option>
                                            <option value="7" <?= $current_month === 7 ? 'selected' : '' ?>>Julio</option>
                                            <option value="8" <?= $current_month === 8 ? 'selected' : '' ?>>Agosto</option>
                                            <option value="9" <?= $current_month === 9 ? 'selected' : '' ?>>Septiembre</option>
                                            <option value="10" <?= $current_month === 10 ? 'selected' : '' ?>>Octubre</option>
                                            <option value="11" <?= $current_month === 11 ? 'selected' : '' ?>>Noviembre</option>
                                            <option value="12" <?= $current_month === 12 ? 'selected' : '' ?>>Diciembre</option>
                                        </select>
                                    </div>

                                    <!-- Year Filter -->
                                    <div class="col-md-2">
                                        <label for="anio" class="form-label">Año: <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="anio" name="anio" 
                                               min="2020" max="2050" value="<?= $current_year ?>" required>
                                    </div>

                                    <!-- Search Button -->
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fa fa-search fa-fw me-1"></i> Buscar
                                        </button>
                                        <button type="button" class="btn btn-secondary" id="btn-limpiar">
                                            <i class="fa fa-eraser fa-fw me-1"></i> Limpiar
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <?php #endregion SEARCH FILTERS ?>

                    <?php #region RESULTS ?>
                    <!-- Results Panel -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Balance</h4>
                            <div class="panel-heading-btn">
                                <a href="gastos-fijos" class="btn btn-info btn-sm me-2">
                                    <i class="fa fa-list me-1"></i>
                                    Gestión de Gastos Fijos
                                </a>
                                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createBalanceModal">
                                    <i class="fa fa-plus me-1"></i>
                                    Agregar gasto fijo a balance
                                </button>
                            </div>
                        </div>
                        <div>
                            <!-- No results message -->
                            <div id="no-results" class="text-center py-4">
                                <i class="fa fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Utilice los filtros para buscar balances de gastos fijos</h5>
                                <p class="text-muted">Los resultados aparecerán aquí una vez que realice una búsqueda.</p>
                            </div>

                            <!-- Loading indicator -->
                            <div id="loading" class="text-center py-4" style="display: none;">
                                <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                                <h5 class="text-primary">Buscando balances...</h5>
                            </div>

                            <!-- Results table -->
                            <div id="results-table-container" style="display: none;">
                                <table class="table table-hover table-sm mb-0" id="balances-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th class="text-center" style="width: 120px;">Acciones</th>
                                            <th>Gasto Fijo</th>
                                            <th class="text-center" style="width: 100px;">Mes</th>
                                            <th class="text-center" style="width: 80px;">Año</th>
                                            <th class="text-end" style="width: 150px;">Valor</th>
                                        </tr>
                                    </thead>
                                    <tbody id="balances-table-body">
                                        <!-- Results will be populated via AJAX -->
                                    </tbody>
                                    <tfoot id="balances-table-footer" style="display: none;">
                                        <tr class="table-info">
                                            <th colspan="4" class="text-end fw-bold">Total:</th>
                                            <th class="text-end fw-bold" id="total-valor"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php #endregion RESULTS ?>

    </div>
    <!-- END #content -->

    <?php #region Create Balance Modal ?>
    <div class="modal fade" id="createBalanceModal" tabindex="-1" aria-labelledby="createBalanceModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="create-balance-form">
                    <div class="modal-header">
                        <h5 class="modal-title" id="createBalanceModalLabel">Crear Nuevo Balance</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="create-id-gasto-fijo" class="form-label">Gasto Fijo: <span class="text-danger">*</span></label>
                            <select class="form-select" id="create-id-gasto-fijo" name="id_gasto_fijo" required>
                                <option value="">Seleccione un gasto fijo</option>
                                <?php foreach ($gastos_fijos as $gasto_fijo): ?>
                                    <option value="<?= $gasto_fijo->getId() ?>"><?= htmlspecialchars($gasto_fijo->getDescripcion()) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="create-mes" class="form-label">Mes: <span class="text-danger">*</span></label>
                                    <select class="form-select" id="create-mes" name="mes" required>
                                        <option value="">Seleccione mes</option>
                                        <option value="1" <?= $current_month === 1 ? 'selected' : '' ?>>Enero</option>
                                        <option value="2" <?= $current_month === 2 ? 'selected' : '' ?>>Febrero</option>
                                        <option value="3" <?= $current_month === 3 ? 'selected' : '' ?>>Marzo</option>
                                        <option value="4" <?= $current_month === 4 ? 'selected' : '' ?>>Abril</option>
                                        <option value="5" <?= $current_month === 5 ? 'selected' : '' ?>>Mayo</option>
                                        <option value="6" <?= $current_month === 6 ? 'selected' : '' ?>>Junio</option>
                                        <option value="7" <?= $current_month === 7 ? 'selected' : '' ?>>Julio</option>
                                        <option value="8" <?= $current_month === 8 ? 'selected' : '' ?>>Agosto</option>
                                        <option value="9" <?= $current_month === 9 ? 'selected' : '' ?>>Septiembre</option>
                                        <option value="10" <?= $current_month === 10 ? 'selected' : '' ?>>Octubre</option>
                                        <option value="11" <?= $current_month === 11 ? 'selected' : '' ?>>Noviembre</option>
                                        <option value="12" <?= $current_month === 12 ? 'selected' : '' ?>>Diciembre</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="create-anio" class="form-label">Año: <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="create-anio" name="anio" 
                                           min="2020" max="2050" value="<?= $current_year ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="create-valor" class="form-label">Valor: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control currency-input" id="create-valor" name="valor"
                                   data-type="currency" placeholder="$0" required>
                        </div>

                        <div class="alert alert-danger" id="create-balance-error" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success">Agregar gasto</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion Create Balance Modal ?>

    <!-- BEGIN scroll-top-btn -->
    <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
        <i class="fa fa-angle-up"></i>
    </a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="toast-notification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="toast-icon" class="fa fa-check-circle text-success me-2"></i>
            <strong id="toast-title" class="me-auto">Notificación</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toast-message">
            Mensaje de notificación
        </div>
    </div>
</div>

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include formatcurrency.js for currency formatting -->
<script src="<?php echo RUTA_RESOURCES; ?>js/formatcurrency.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal Elements
    const createBalanceModal = new bootstrap.Modal(document.getElementById('createBalanceModal'));

    // Form Elements
    const createBalanceForm     = document.getElementById('create-balance-form');
    const searchForm            = document.getElementById('search-form');
    const btnLimpiar            = document.getElementById('btn-limpiar');
    const noResults             = document.getElementById('no-results');
    const loading               = document.getElementById('loading');
    const resultsTableContainer = document.getElementById('results-table-container');
    const balancesTableBody     = document.getElementById('balances-table-body');
    const balancesTableFooter   = document.getElementById('balances-table-footer');
    const totalValor            = document.getElementById('total-valor');

    // Current date for validation
    const currentDate  = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear  = currentDate.getFullYear();

    // Store gastos fijos data for valor pre-loading
    const gastosFijosData = <?= json_encode(array_map(function($gf) {
        return ['id' => $gf->getId(), 'valor' => $gf->getValor()];
    }, $gastos_fijos)) ?>;

    // Initialize currency formatting for modals
    $('#createBalanceModal').on('shown.bs.modal', function () {
        $('input[data-type="currency"]').each(function() {
            $(this).on({
                keyup: function() { formatCurrency($(this)); },
                blur: function() { formatCurrency($(this), "blur"); }
            });
        });
    });

    // Pre-load valor when gasto fijo is selected
    document.getElementById('create-id-gasto-fijo').addEventListener('change', function() {
        const selectedId = this.value;
        const valorInput = document.getElementById('create-valor');

        if (selectedId) {
            const gastoFijo = gastosFijosData.find(gf => gf.id == selectedId);
            if (gastoFijo) {
                valorInput.value = '$' + Math.round(gastoFijo.valor).toLocaleString('es-CO').replace(/,/g, '.');
            }
        } else {
            valorInput.value = '';
        }
    });

    // Auto-search on page load since month and year are pre-loaded
    searchBalances();

    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        searchBalances();
    });

    // Clear button
    btnLimpiar.addEventListener('click', function() {
        searchForm.reset();
        noResults.style.display             = 'block';
        resultsTableContainer.style.display = 'none';
        balancesTableFooter.style.display   = 'none';
    });

    // Create Balance Form
    createBalanceForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(createBalanceForm);
        formData.append('action', 'create_balance');

        fetch('balances-gastos-fijos', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createBalanceModal.hide();
                createBalanceForm.reset();
                document.getElementById('create-balance-error').style.display = 'none';

                // Show success message
                showToastNotification('success', 'Éxito', data.message);

                // Auto-refresh table if search criteria are valid, otherwise show no results
                if (hasValidSearchCriteria()) {
                    searchBalances();
                } else {
                    showNoResults();
                }
            } else {
                document.getElementById('create-balance-error').textContent = data.message;
                document.getElementById('create-balance-error').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('create-balance-error').textContent = 'Error de conexión al crear el balance.';
            document.getElementById('create-balance-error').style.display = 'block';
        });
    });

    // Helper function to check if valid search criteria exist
    function hasValidSearchCriteria() {
        const mes  = document.getElementById('mes').value;
        const anio = document.getElementById('anio').value;
        return mes && anio;
    }

    // Make hasValidSearchCriteria globally accessible for deleteBalance function
    window.hasValidSearchCriteria = hasValidSearchCriteria;

    // Search function
    function searchBalances() {
        const formData = new FormData(searchForm);
        formData.append('action', 'search_balances');

        // Validate required fields (only mes and anio are mandatory)
        const mes  = formData.get('mes');
        const anio = formData.get('anio');

        if (!mes || !anio) {
            swal({
                title : "Campos Requeridos",
                text  : "Los filtros de mes y año son obligatorios para realizar la búsqueda.",
                icon  : "warning",
                button: {
                    text      : "Entendido",
                    value     : null,
                    visible   : true,
                    className : "btn-warning",
                    closeModal: true
                }
            });
            return;
        }

        // Show loading
        noResults.style.display             = 'none';
        resultsTableContainer.style.display = 'none';
        balancesTableFooter.style.display   = 'none';
        loading.style.display               = 'block';

        fetch('balances-gastos-fijos', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';

            if (data.success) {
                if (data.balances.length > 0) {
                    displayResults(data.balances, data.total_valor_formateado);
                } else {
                    showNoResults();
                }
            } else {
                swal({
                    title : "Error",
                    text  : data.message || 'Error al buscar balances',
                    icon  : "error",
                    button: {
                        text      : "Cerrar",
                        value     : null,
                        visible   : true,
                        className : "btn-danger",
                        closeModal: true
                    }
                });
                showNoResults();
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            swal({
                title : "Error de Conexión",
                text  : 'Error de conexión al buscar balances',
                icon  : "error",
                button: {
                    text      : "Cerrar",
                    value     : null,
                    visible   : true,
                    className : "btn-danger",
                    closeModal: true
                }
            });
            showNoResults();
            console.error('Error:', error);
        });
    }

    // Make searchBalances globally accessible for deleteBalance function
    window.searchBalances = searchBalances;

    // Display results in table
    function displayResults(balances, totalFormateado) {
        balancesTableBody.innerHTML = '';

        balances.forEach(balance => {
            const row = document.createElement('tr');

            // Check if balance can be deleted (current and future months/years only)
            const recordDate = new Date(balance.anio, balance.mes - 1, 1); // Create date for comparison
            const currentDate = new Date(currentYear, currentMonth - 1, 1);
            const canDelete = recordDate >= currentDate; // Allow current and future periods

            const deleteButton = canDelete ?
                `<button class="btn btn-danger btn-xs" onclick="eliminarBalance(${balance.id}, '${balance.gasto_fijo_descripcion}', ${balance.mes}, ${balance.anio})" title="Eliminar balance">
                    <i class="fa fa-trash"></i>
                </button>` :
                `<button class="btn btn-secondary btn-xs" disabled title="Solo se pueden eliminar balances del período actual y futuros">
                    <i class="fa fa-ban"></i>
                </button>`;

            row.innerHTML = `
                <td class="text-center align-middle">
                    ${deleteButton}
                </td>
                <td class="align-middle">${balance.gasto_fijo_descripcion}</td>
                <td class="text-center align-middle">${balance.nombre_mes}</td>
                <td class="text-center align-middle">${balance.anio}</td>
                <td class="text-end align-middle">${balance.valor_formateado}</td>
            `;
            balancesTableBody.appendChild(row);
        });

        // Show total
        totalValor.textContent            = totalFormateado;
        balancesTableFooter.style.display = 'table-footer-group';

        resultsTableContainer.style.display = 'block';
        noResults.style.display             = 'none';
    }

    // Show no results message
    function showNoResults() {
        noResults.innerHTML = `
            <i class="fa fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No se encontraron balances</h5>
            <p class="text-muted">Intente modificar los criterios de búsqueda.</p>
        `;
        noResults.style.display             = 'block';
        resultsTableContainer.style.display = 'none';
        balancesTableFooter.style.display   = 'none';
    }

    // Toast notification function
    function showToastNotification(type, title, message) {
        const toast = document.getElementById('toast-notification');
        const toastIcon = document.getElementById('toast-icon');
        const toastTitle = document.getElementById('toast-title');
        const toastMessage = document.getElementById('toast-message');

        // Configure toast based on type
        if (type === 'success') {
            toastIcon.className = 'fa fa-check-circle text-success me-2';
            toast.className = 'toast border-success';
        } else if (type === 'error') {
            toastIcon.className = 'fa fa-exclamation-circle text-danger me-2';
            toast.className = 'toast border-danger';
        } else if (type === 'warning') {
            toastIcon.className = 'fa fa-exclamation-triangle text-warning me-2';
            toast.className = 'toast border-warning';
        } else {
            toastIcon.className = 'fa fa-info-circle text-info me-2';
            toast.className = 'toast border-info';
        }

        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // Show toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 4000
        });
        bsToast.show();
    }
});

// Global function for delete button
window.eliminarBalance = function(id, descripcion, mes, anio) {
    // Get current date for validation using Colombian timezone
    const currentDate = new Date();
    currentDate.setTime(currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000) + (-5 * 3600000)); // Convert to Colombia time (UTC-5)
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear  = currentDate.getFullYear();

    // Create date objects for comparison
    const recordDate = new Date(anio, mes - 1, 1);
    const currentPeriodDate = new Date(currentYear, currentMonth - 1, 1);

    // Check if record is from a past period (before current month/year)
    if (recordDate < currentPeriodDate) {
        swal({
            title : "Acción No Permitida",
            text  : "Solo se pueden eliminar balances del período actual y futuros. No se pueden modificar datos históricos.",
            icon  : "warning",
            button: {
                text      : "Entendido",
                value     : null,
                visible   : true,
                className : "btn-warning",
                closeModal: true
            }
        });
        return;
    }

    const meses = ['', 'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
                   'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];

    swal({
        title  : "¿Está seguro?",
        text   : `¿Desea eliminar el balance de "${descripcion}" para ${meses[mes]} ${anio}? Esta acción no se puede deshacer.`,
        icon   : "warning",
        buttons: {
            cancel: {
                text      : "Cancelar",
                value     : null,
                visible   : true,
                className : "btn-secondary",
                closeModal: true,
            },
            confirm: {
                text      : "Sí, eliminar",
                value     : true,
                visible   : true,
                className : "btn-danger",
                closeModal: true
            }
        }
    }).then((willDelete) => {
        if (willDelete) {
            deleteBalance(id);
        }
    });
};

function deleteBalance(id) {
    const formData = new FormData();
    formData.append('action', 'delete_balance');
    formData.append('id', id);

    fetch('balances-gastos-fijos', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show toast notification
            const toast = document.getElementById('toast-notification');
            const toastIcon = document.getElementById('toast-icon');
            const toastTitle = document.getElementById('toast-title');
            const toastMessage = document.getElementById('toast-message');

            toastIcon.className = 'fa fa-check-circle text-success me-2';
            toast.className = 'toast border-success';
            toastTitle.textContent = 'Éxito';
            toastMessage.textContent = data.message;

            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 4000
            });
            bsToast.show();

            // Auto-refresh table if search criteria are valid, otherwise show no results
            setTimeout(() => {
                if (hasValidSearchCriteria()) {
                    searchBalances();
                } else {
                    document.getElementById('no-results').style.display             = 'block';
                    document.getElementById('results-table-container').style.display = 'none';
                    document.getElementById('balances-table-footer').style.display   = 'none';
                }
            }, 1000);
        } else {
            swal({
                title : "Error",
                text  : data.message,
                icon  : "error",
                button: {
                    text      : "Cerrar",
                    value     : null,
                    visible   : true,
                    className : "btn-danger",
                    closeModal: true
                }
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        swal({
            title : "Error de Conexión",
            text  : 'Error de conexión al eliminar el balance',
            icon  : "error",
            button: {
                text      : "Cerrar",
                value     : null,
                visible   : true,
                className : "btn-danger",
                closeModal: true
            }
        });
    });
}
</script>
<?php #endregion JS ?>
